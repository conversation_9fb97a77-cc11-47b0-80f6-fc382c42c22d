<template>
  <div class="test-page">
    <!-- 算法选择 -->
    <el-card v-if="!selectedAlgorithm">
      <template #header>
        <div class="card-header">
          <span>🧪 在线测试</span>
          <el-button type="primary" @click="refreshAlgorithms" :loading="loading">
            刷新算法列表
          </el-button>
        </div>
      </template>

      <div v-if="loading" class="loading-container">
        <el-skeleton :rows="3" animated />
      </div>

      <div v-else-if="algorithms.length === 0" class="empty-container">
        <el-empty description="暂无可测试的算法">
          <el-button type="primary" @click="refreshAlgorithms">刷新列表</el-button>
        </el-empty>
      </div>

      <div v-else class="algorithms-grid">
        <el-card 
          v-for="algorithm in runningAlgorithms" 
          :key="algorithm.id"
          class="algorithm-card"
          :class="{ 'selected': selectedAlgorithm?.id === algorithm.id }"
          @click="selectAlgorithm(algorithm)"
          shadow="hover"
        >
          <div class="algorithm-info">
            <div class="algorithm-header">
              <h3>{{ algorithm.name }}</h3>
              <el-tag :type="algorithm.status === 'running' ? 'success' : 'danger'" size="small">
                {{ algorithm.status === 'running' ? '运行中' : '已停止' }}
              </el-tag>
            </div>
            <p class="algorithm-description">{{ algorithm.description }}</p>
            <div class="algorithm-details">
              <span><strong>版本:</strong> {{ algorithm.version }}</span>
              <span><strong>类型:</strong> {{ algorithm.algorithm_type }}</span>
              <span><strong>端口:</strong> {{ algorithm.ports || '未配置' }}</span>
            </div>
          </div>
          <div class="algorithm-actions">
            <el-button type="primary" size="small">
              选择测试
            </el-button>
          </div>
        </el-card>
      </div>
    </el-card>

    <!-- 算法测试界面 -->
    <div v-else class="test-interface">
      <!-- 算法信息头部 -->
      <el-card class="algorithm-header-card">
        <div class="algorithm-header-info">
          <div class="algorithm-basic-info">
            <h2>{{ selectedAlgorithm.name }}</h2>
            <p>{{ selectedAlgorithm.description }}</p>
            <div class="algorithm-meta">
              <el-tag type="success" size="small">{{ selectedAlgorithm.status === 'running' ? '运行中' : '已停止' }}</el-tag>
              <span>版本: {{ selectedAlgorithm.version }}</span>
              <span>类型: {{ selectedAlgorithm.algorithm_type }}</span>
            </div>
          </div>
          <div class="algorithm-actions">
            <el-button @click="goBack">
              返回选择
            </el-button>
            <el-button type="primary" @click="refreshFunctions" :loading="functionsLoading">
              刷新功能
            </el-button>
          </div>
        </div>
      </el-card>

      <!-- 功能列表和测试区域 -->
      <div class="test-content">
        <!-- 功能列表 -->
        <el-card class="functions-card">
          <template #header>
            <span>🔧 可用功能</span>
          </template>

          <div v-if="functionsLoading" class="loading-container">
            <el-skeleton :rows="2" animated />
          </div>

          <div v-else-if="algorithmFunctions.length === 0" class="empty-container">
            <el-empty description="暂无可用功能" size="small">
              <el-button type="primary" size="small" @click="refreshFunctions">刷新功能</el-button>
            </el-empty>
          </div>

          <div v-else class="functions-list">
            <div 
              v-for="func in algorithmFunctions" 
              :key="func.name"
              class="function-item"
              :class="{ 'active': selectedFunction?.name === func.name }"
              @click="selectFunction(func)"
            >
              <div class="function-info">
                <h4>{{ func.name }}</h4>
                <p>{{ func.description }}</p>
                <div class="function-meta">
                  <el-tag size="small">{{ func.method }}</el-tag>
                  <span>{{ func.path }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 测试区域 -->
        <el-card class="test-area-card">
          <template #header>
            <div class="test-area-header">
              <span>🚀 功能测试</span>
              <el-button-group v-if="selectedFunction">
                <el-button
                  :type="activeTab === 'test' ? 'primary' : ''"
                  size="small"
                  @click="activeTab = 'test'"
                >
                  在线测试
                </el-button>
                <el-button
                  :type="activeTab === 'docs' ? 'primary' : ''"
                  size="small"
                  @click="activeTab = 'docs'"
                >
                  API文档
                </el-button>
              </el-button-group>
            </div>
          </template>

          <div v-if="!selectedFunction" class="empty-container">
            <el-empty description="请选择要测试的功能" size="small" />
          </div>

          <!-- 在线测试标签页 -->
          <div v-else-if="activeTab === 'test'" class="test-form">
            <h3>{{ selectedFunction.name }}</h3>
            <p>{{ selectedFunction.description }}</p>

            <!-- 参数输入 -->
            <div class="parameters-section">
              <h4>参数配置</h4>
              <div v-if="selectedFunction.parameters && selectedFunction.parameters.length > 0">
                <div
                  v-for="param in selectedFunction.parameters"
                  :key="param.name"
                  class="parameter-item"
                >
                  <label>{{ param.display_name || param.name }} <span v-if="param.required" class="required">*</span></label>

                  <!-- 布尔类型参数 - 使用下拉框 -->
                  <el-select
                    v-if="param.type === 'boolean' && param.options"
                    v-model="testParams[param.name]"
                    :placeholder="param.description"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="option in param.options"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>

                  <!-- 文件类型参数 - 支持图片上传 -->
                  <el-upload
                    v-else-if="param.type === 'file'"
                    class="upload-demo"
                    :auto-upload="false"
                    :on-change="(file) => handleFileChange(param.name, file)"
                    :show-file-list="true"
                    :limit="1"
                    accept="image/*"
                    list-type="picture"
                  >
                    <el-button size="small" type="primary">
                      <el-icon><Upload /></el-icon>
                      选择图片
                    </el-button>
                    <template #tip>
                      <div class="el-upload__tip">{{ param.description }}</div>
                    </template>
                  </el-upload>

                  <!-- 字符串类型参数 -->
                  <el-input
                    v-else-if="param.type === 'string' || param.type === 'text'"
                    v-model="testParams[param.name]"
                    :placeholder="param.description"
                    :required="param.required"
                  />

                  <!-- 数字类型参数 -->
                  <el-input-number
                    v-else-if="param.type === 'number' || param.type === 'integer'"
                    v-model="testParams[param.name]"
                    :placeholder="param.description"
                    :required="param.required"
                    style="width: 100%"
                  />

                  <!-- 其他类型参数 -->
                  <el-input
                    v-else
                    v-model="testParams[param.name]"
                    :placeholder="param.description"
                    :required="param.required"
                  />

                  <small>{{ param.description }}</small>
                </div>
              </div>
              <div v-else>
                <p>此功能无需参数</p>
              </div>
            </div>

            <!-- 测试按钮 -->
            <div class="test-actions">
              <el-button
                type="primary"
                @click="executeTest"
                :loading="testLoading"
                :disabled="!canExecuteTest"
              >
                执行测试
              </el-button>
              <el-button @click="clearParams">
                清空参数
              </el-button>
            </div>

            <!-- 测试结果 -->
            <div v-if="testResult" class="test-result">
              <h4>测试结果</h4>
              <div class="result-header">
                <el-tag :type="testResult.success ? 'success' : 'danger'">
                  {{ testResult.success ? '成功' : '失败' }}
                </el-tag>
                <span class="result-time">{{ testResult.timestamp }}</span>
              </div>
              <div class="result-content">
                <pre>{{ JSON.stringify(testResult.data, null, 2) }}</pre>
              </div>
            </div>
          </div>

          <!-- API文档标签页 -->
          <div v-else-if="activeTab === 'docs'" class="api-docs">
            <div class="api-docs-content">
              <h3>{{ selectedFunction.name }} - API调用文档</h3>
              <p class="api-description">{{ selectedFunction.description }}</p>

              <!-- API基本信息 -->
              <el-card class="api-info-card" shadow="never">
                <template #header>
                  <span>📋 基本信息</span>
                </template>
                <div class="api-basic-info">
                  <div class="info-item">
                    <label>请求方法:</label>
                    <el-tag :type="getMethodTagType(selectedFunction.method)" size="small">
                      {{ selectedFunction.method }}
                    </el-tag>
                  </div>
                  <div class="info-item">
                    <label>API路径:</label>
                    <code class="api-path">{{ selectedFunction.path }}</code>
                  </div>
                  <div class="info-item">
                    <label>完整URL:</label>
                    <code class="api-url">{{ getFullApiUrl(selectedFunction.path) }}</code>
                    <el-button
                      size="small"
                      type="primary"
                      link
                      @click="copyToClipboard(getFullApiUrl(selectedFunction.path))"
                    >
                      复制
                    </el-button>
                  </div>
                </div>
              </el-card>

              <!-- 请求参数 -->
              <el-card class="api-params-card" shadow="never">
                <template #header>
                  <span>📝 请求参数</span>
                </template>
                <div v-if="selectedFunction.parameters && selectedFunction.parameters.length > 0">
                  <el-table :data="selectedFunction.parameters" style="width: 100%">
                    <el-table-column label="参数名" width="120">
                      <template #default="scope">
                        <div>
                          <code>{{ scope.row.name }}</code>
                          <div v-if="scope.row.display_name && scope.row.display_name !== scope.row.name"
                               style="font-size: 12px; color: #666; margin-top: 2px;">
                            {{ scope.row.display_name }}
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column prop="type" label="类型" width="80">
                      <template #default="scope">
                        <el-tag size="small" :type="getParamTypeColor(scope.row.type)">
                          {{ scope.row.type }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="required" label="必需" width="60">
                      <template #default="scope">
                        <el-tag :type="scope.row.required ? 'danger' : 'info'" size="small">
                          {{ scope.row.required ? '是' : '否' }}
                        </el-tag>
                      </template>
                    </el-table-column>
                    <el-table-column prop="default" label="默认值" width="100">
                      <template #default="scope">
                        <code v-if="scope.row.default !== undefined">{{ scope.row.default }}</code>
                        <span v-else class="text-muted">-</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="description" label="说明" min-width="200">
                      <template #default="scope">
                        {{ scope.row.description }}
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <div v-else class="no-params">
                  <el-empty description="此API无需参数" size="small" />
                </div>
              </el-card>

              <!-- 请求示例 -->
              <el-card class="api-example-card" shadow="never">
                <template #header>
                  <div class="example-header">
                    <span>💻 请求示例</span>
                    <el-button-group size="small">
                      <el-button
                        :type="exampleType === 'curl' ? 'primary' : ''"
                        @click="exampleType = 'curl'"
                      >
                        cURL
                      </el-button>
                      <el-button
                        :type="exampleType === 'javascript' ? 'primary' : ''"
                        @click="exampleType = 'javascript'"
                      >
                        JavaScript
                      </el-button>
                      <el-button
                        :type="exampleType === 'python' ? 'primary' : ''"
                        @click="exampleType = 'python'"
                      >
                        Python
                      </el-button>
                    </el-button-group>
                  </div>
                </template>
                <div class="code-example">
                  <div class="code-header">
                    <span>{{ getExampleTitle(exampleType) }}</span>
                    <el-button
                      size="small"
                      type="primary"
                      link
                      @click="copyToClipboard(getApiExample(exampleType))"
                    >
                      复制代码
                    </el-button>
                  </div>
                  <pre class="code-block"><code>{{ getApiExample(exampleType) }}</code></pre>
                </div>
              </el-card>

              <!-- 响应示例 -->
              <el-card class="api-response-card" shadow="never">
                <template #header>
                  <span>📤 响应格式</span>
                </template>
                <div class="response-example">
                  <h5>成功响应示例:</h5>
                  <pre class="code-block"><code>{{ getSuccessResponseExample() }}</code></pre>

                  <h5>错误响应示例:</h5>
                  <pre class="code-block"><code>{{ getErrorResponseExample() }}</code></pre>
                </div>
              </el-card>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload } from '@element-plus/icons-vue'

// 响应式数据
const loading = ref(false)
const functionsLoading = ref(false)
const testLoading = ref(false)
const algorithms = ref([])
const selectedAlgorithm = ref(null)
const algorithmFunctions = ref([])
const selectedFunction = ref(null)
const testParams = ref({})
const testResult = ref(null)
const activeTab = ref('test') // 当前活动标签页
const exampleType = ref('curl') // 示例代码类型

// 计算属性
const runningAlgorithms = computed(() => {
  return algorithms.value.filter(algo => algo.status === 'running')
})

const canExecuteTest = computed(() => {
  if (!selectedFunction.value) return false
  
  // 检查必需参数是否都已填写
  if (selectedFunction.value.parameters) {
    return selectedFunction.value.parameters
      .filter(param => param.required)
      .every(param => testParams.value[param.name])
  }
  
  return true
})

// 方法
const refreshAlgorithms = async () => {
  loading.value = true
  try {
    const response = await fetch('/api/algorithms/')
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        algorithms.value = data.algorithms
      } else {
        ElMessage.error(data.message || '获取算法列表失败')
      }
    }
  } catch (error) {
    console.error('获取算法列表失败:', error)
    ElMessage.error('获取算法列表失败')
  } finally {
    loading.value = false
  }
}

const selectAlgorithm = async (algorithm) => {
  selectedAlgorithm.value = algorithm
  await refreshFunctions()
}

const goBack = () => {
  selectedAlgorithm.value = null
  selectedFunction.value = null
  algorithmFunctions.value = []
  testParams.value = {}
  testResult.value = null
}

const refreshFunctions = async () => {
  if (!selectedAlgorithm.value) return
  
  functionsLoading.value = true
  try {
    const response = await fetch(`/api/algorithms/${selectedAlgorithm.value.id}/functions`)
    if (response.ok) {
      const data = await response.json()
      if (data.success) {
        algorithmFunctions.value = data.functions
      } else {
        ElMessage.error(data.message || '获取算法功能失败')
      }
    }
  } catch (error) {
    console.error('获取算法功能失败:', error)
    ElMessage.error('获取算法功能失败')
  } finally {
    functionsLoading.value = false
  }
}

const selectFunction = (func) => {
  selectedFunction.value = func
  testParams.value = {}
  testResult.value = null
  activeTab.value = 'test' // 选择功能时默认显示测试标签页

  // 初始化参数默认值
  if (func.parameters) {
    func.parameters.forEach(param => {
      if (param.default !== undefined) {
        testParams.value[param.name] = param.default
      }
    })
  }
}

const handleFileChange = (paramName, file) => {
  testParams.value[paramName] = file.raw
}

const clearParams = () => {
  testParams.value = {}
  testResult.value = null
}

const executeTest = async () => {
  if (!selectedAlgorithm.value || !selectedFunction.value) return
  
  testLoading.value = true
  testResult.value = null
  
  try {
    const formData = new FormData()
    
    // 添加参数到FormData
    Object.keys(testParams.value).forEach(key => {
      const value = testParams.value[key]
      if (value instanceof File) {
        formData.append(key, value)
      } else {
        formData.append(key, JSON.stringify(value))
      }
    })
    
    const response = await fetch(
      `/api/algorithms/${selectedAlgorithm.value.id}/test/${selectedFunction.value.name}`,
      {
        method: 'POST',
        body: formData
      }
    )
    
    const data = await response.json()
    
    testResult.value = {
      success: response.ok && data.success,
      data: data,
      timestamp: new Date().toLocaleString()
    }
    
    if (testResult.value.success) {
      ElMessage.success('测试执行成功')
    } else {
      ElMessage.error(data.message || '测试执行失败')
    }
    
  } catch (error) {
    console.error('测试执行失败:', error)
    testResult.value = {
      success: false,
      data: { error: error.message },
      timestamp: new Date().toLocaleString()
    }
    ElMessage.error('测试执行失败')
  } finally {
    testLoading.value = false
  }
}

// API文档相关方法
const getMethodTagType = (method) => {
  const types = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger'
  }
  return types[method] || 'info'
}

const getParamTypeColor = (type) => {
  const colors = {
    'file': 'warning',
    'string': 'primary',
    'number': 'success',
    'boolean': 'info'
  }
  return colors[type] || 'info'
}

const getFullApiUrl = (path) => {
  if (!selectedAlgorithm.value) return path

  // 获取算法容器的端口
  const port = selectedAlgorithm.value.port || 8001
  return `http://localhost:${port}${path}`
}

const getExampleTitle = (type) => {
  const titles = {
    'curl': 'cURL 命令行',
    'javascript': 'JavaScript (Fetch API)',
    'python': 'Python (requests)'
  }
  return titles[type] || type
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败')
  }
}

const getApiExample = (type) => {
  if (!selectedFunction.value) return ''

  const url = getFullApiUrl(selectedFunction.value.path)
  const method = selectedFunction.value.method
  const params = selectedFunction.value.parameters || []

  if (type === 'curl') {
    return generateCurlExample(url, method, params)
  } else if (type === 'javascript') {
    return generateJavaScriptExample(url, method, params)
  } else if (type === 'python') {
    return generatePythonExample(url, method, params)
  }

  return ''
}

const generateCurlExample = (url, method, params) => {
  let example = `curl -X ${method} "${url}"`

  if (method === 'POST' && params.length > 0) {
    example += ' \\\n'

    const fileParams = params.filter(p => p.type === 'file')
    const otherParams = params.filter(p => p.type !== 'file')

    // 文件参数
    fileParams.forEach(param => {
      example += `  -F "${param.name}=@example.jpg" \\\n`
    })

    // 其他参数
    otherParams.forEach(param => {
      const value = param.default || (param.type === 'string' ? 'example_value' : 'true')
      example += `  -F "${param.name}=${value}" \\\n`
    })

    // 移除最后的反斜杠
    example = example.replace(/ \\\n$/, '')
  }

  return example
}

const generateJavaScriptExample = (url, method, params) => {
  let example = ''

  if (method === 'GET') {
    example = `fetch('${url}')
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));`
  } else {
    const hasFiles = params.some(p => p.type === 'file')

    if (hasFiles) {
      example = `const formData = new FormData();
`
      params.forEach(param => {
        if (param.type === 'file') {
          example += `formData.append('${param.name}', fileInput.files[0]); // 文件输入
`
        } else {
          const value = param.default || (param.type === 'string' ? 'example_value' : 'true')
          example += `formData.append('${param.name}', '${value}');
`
        }
      })

      example += `
fetch('${url}', {
  method: '${method}',
  body: formData
})
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));`
    } else {
      const jsonData = {}
      params.forEach(param => {
        const value = param.default || (param.type === 'string' ? 'example_value' : true)
        jsonData[param.name] = value
      })

      example = `fetch('${url}', {
  method: '${method}',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(${JSON.stringify(jsonData, null, 2)})
})
  .then(response => response.json())
  .then(data => console.log(data))
  .catch(error => console.error('Error:', error));`
    }
  }

  return example
}

const generatePythonExample = (url, method, params) => {
  let example = `import requests

`

  if (method === 'GET') {
    example += `response = requests.get('${url}')
print(response.json())`
  } else {
    const hasFiles = params.some(p => p.type === 'file')

    if (hasFiles) {
      example += `# 准备文件和数据
files = {
`
      const fileParams = params.filter(p => p.type === 'file')
      const otherParams = params.filter(p => p.type !== 'file')

      fileParams.forEach(param => {
        example += `    '${param.name}': open('example.jpg', 'rb'),
`
      })

      example += `}

`

      if (otherParams.length > 0) {
        example += `data = {
`
        otherParams.forEach(param => {
          const value = param.default || (param.type === 'string' ? 'example_value' : 'true')
          example += `    '${param.name}': '${value}',
`
        })
        example += `}

response = requests.${method.toLowerCase()}('${url}', files=files, data=data)`
      } else {
        example += `response = requests.${method.toLowerCase()}('${url}', files=files)`
      }
    } else {
      const jsonData = {}
      params.forEach(param => {
        const value = param.default || (param.type === 'string' ? 'example_value' : true)
        jsonData[param.name] = value
      })

      example += `data = ${JSON.stringify(jsonData, null, 2)}

response = requests.${method.toLowerCase()}('${url}', json=data)`
    }

    example += `
print(response.json())`
  }

  return example
}

const getSuccessResponseExample = () => {
  if (!selectedFunction.value) return ''

  const functionName = selectedFunction.value.name

  if (functionName === 'health_check') {
    return JSON.stringify({
      "success": true,
      "message": "服务运行正常",
      "data": {
        "status": "healthy",
        "algorithm": "wenzhou_face",
        "version": "1.0.0",
        "timestamp": "2025-07-28T10:30:00Z"
      },
      "timestamp": "2025-07-28T10:30:00Z",
      "processing_time": 0.001
    }, null, 2)
  } else if (functionName === 'face_detection') {
    return JSON.stringify({
      "success": true,
      "message": "人脸检测完成",
      "data": {
        "faces": [
          {
            "bbox": [100, 150, 200, 250],
            "confidence": 0.95,
            "landmarks": [[120, 170], [180, 170], [150, 200], [130, 220], [170, 220]],
            "features": [0.1, 0.2, 0.3, "..."],
            "quality_score": 0.88
          }
        ],
        "face_count": 1
      },
      "timestamp": "2025-07-28T10:30:00Z",
      "processing_time": 0.234
    }, null, 2)
  } else if (functionName === 'face_comparison') {
    return JSON.stringify({
      "success": true,
      "message": "人脸比对完成",
      "data": {
        "similarity": 0.92,
        "is_same_person": true,
        "confidence": 0.95,
        "threshold": 0.8
      },
      "timestamp": "2025-07-28T10:30:00Z",
      "processing_time": 0.156
    }, null, 2)
  } else {
    return JSON.stringify({
      "success": true,
      "message": "处理完成",
      "data": {
        "result": "处理结果数据"
      },
      "timestamp": "2025-07-28T10:30:00Z",
      "processing_time": 0.123
    }, null, 2)
  }
}

const getErrorResponseExample = () => {
  return JSON.stringify({
    "success": false,
    "message": "处理失败",
    "error": "Invalid input format",
    "timestamp": "2025-07-28T10:30:00Z",
    "processing_time": 0.001
  }, null, 2)
}

// 生命周期
onMounted(() => {
  refreshAlgorithms()
})
</script>

<style scoped>
.test-page {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-container, .empty-container {
  padding: 40px;
  text-align: center;
}

.algorithms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.algorithm-card {
  cursor: pointer;
  transition: all 0.3s;
}

.algorithm-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.algorithm-card.selected {
  border-color: #409eff;
}

.algorithm-info {
  margin-bottom: 15px;
}

.algorithm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.algorithm-header h3 {
  margin: 0;
  color: #303133;
}

.algorithm-description {
  color: #606266;
  margin: 10px 0;
  font-size: 14px;
}

.algorithm-details {
  display: flex;
  flex-direction: column;
  gap: 5px;
  font-size: 12px;
  color: #909399;
}

.algorithm-actions {
  text-align: center;
}

.test-interface {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.algorithm-header-card {
  margin-bottom: 20px;
}

.algorithm-header-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.algorithm-basic-info h2 {
  margin: 0 0 10px 0;
  color: #303133;
}

.algorithm-meta {
  display: flex;
  gap: 15px;
  align-items: center;
  margin-top: 10px;
}

.test-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
}

.functions-card {
  height: fit-content;
}

.functions-list {
  max-height: 600px;
  overflow-y: auto;
}

.function-item {
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.function-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.function-item.active {
  border-color: #409eff;
  background-color: #ecf5ff;
}

.function-info h4 {
  margin: 0 0 5px 0;
  color: #303133;
}

.function-info p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
}

.function-meta {
  display: flex;
  gap: 10px;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

.test-form {
  padding: 20px;
}

.test-form h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.parameters-section {
  margin: 20px 0;
}

.parameters-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.parameter-item {
  margin-bottom: 20px;
}

.parameter-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #303133;
}

.required {
  color: #f56c6c;
}

.parameter-item small {
  display: block;
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

.test-actions {
  margin: 30px 0;
  display: flex;
  gap: 10px;
}

.test-result {
  margin-top: 30px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fafafa;
}

.test-result h4 {
  margin: 0 0 15px 0;
  color: #303133;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.result-time {
  color: #909399;
  font-size: 14px;
}

.result-content {
  background-color: #fff;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
}

.result-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #303133;
}

/* API文档样式 */
.test-area-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.api-docs {
  padding: 20px 0;
}

.api-docs-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.api-description {
  color: #606266;
  font-size: 14px;
  margin-bottom: 20px;
}

.api-info-card,
.api-params-card,
.api-example-card,
.api-response-card {
  margin-bottom: 20px;
}

.api-basic-info {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.info-item label {
  font-weight: 600;
  min-width: 80px;
  color: #303133;
}

.api-path,
.api-url {
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #e6a23c;
  border: 1px solid #ebeef5;
}

.no-params {
  text-align: center;
  padding: 20px;
}

.example-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.code-example {
  margin-top: 10px;
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 4px 4px 0 0;
  border: 1px solid #ebeef5;
  border-bottom: none;
}

.code-block {
  background: #fafafa;
  border: 1px solid #ebeef5;
  border-radius: 0 0 4px 4px;
  padding: 16px;
  margin: 0;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #303133;
}

.response-example h5 {
  margin: 20px 0 10px 0;
  color: #303133;
  font-size: 14px;
}

.response-example h5:first-child {
  margin-top: 0;
}

.text-muted {
  color: #909399;
}
</style>
