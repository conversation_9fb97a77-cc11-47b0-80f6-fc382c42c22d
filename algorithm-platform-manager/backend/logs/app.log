2025-07-25 16:41:52,949 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-25 16:41:52,949 - __main__ - INFO - 🔄 使用内置API实现
2025-07-25 16:41:52,950 - __main__ - INFO - 🔧 注册内置API端点
2025-07-25 16:45:33,298 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-25 16:45:33,298 - __main__ - INFO - 🔄 使用内置API实现
2025-07-25 16:45:33,299 - __main__ - INFO - 🔧 注册内置API端点
2025-07-25 16:45:33,304 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-25 16:45:33,304 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-25 16:45:33,304 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-25 16:45:33,304 - __main__ - INFO - 👥 工作进程: 1
2025-07-25 16:46:20,433 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-25 16:46:20,433 - __main__ - INFO - 🔄 使用内置API实现
2025-07-25 16:46:20,433 - __main__ - INFO - 🔧 注册内置API端点
2025-07-25 16:46:20,435 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-25 16:46:20,435 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-25 16:46:20,435 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-25 16:46:20,435 - __main__ - INFO - 👥 工作进程: 1
2025-07-25 16:46:20,463 - main - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-25 16:46:20,463 - main - INFO - 🔄 使用内置API实现
2025-07-25 16:46:20,463 - main - INFO - 🔧 注册内置API端点
2025-07-25 16:46:20,464 - main - INFO - 🚀 算法管理平台专业版启动中...
2025-07-25 16:46:20,464 - main - INFO - 🎉 算法管理平台专业版启动完成
2025-07-25 16:47:38,695 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-25 16:47:38,695 - __main__ - INFO - 🔄 使用内置API实现
2025-07-25 16:47:38,695 - __main__ - INFO - 🔧 注册内置API端点
2025-07-25 16:47:38,697 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-25 16:47:38,697 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-25 16:47:38,697 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-25 16:47:38,697 - __main__ - INFO - 👥 工作进程: 1
2025-07-25 16:47:38,716 - main - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-25 16:47:38,716 - main - INFO - 🔄 使用内置API实现
2025-07-25 16:47:38,716 - main - INFO - 🔧 注册内置API端点
2025-07-25 16:47:38,717 - main - INFO - 🚀 算法管理平台专业版启动中...
2025-07-25 16:47:38,717 - main - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 11:24:50,190 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 11:24:50,190 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 11:24:50,190 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 11:24:50,191 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 11:24:50,191 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 11:24:50,191 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 11:24:50,191 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 11:24:50,231 - main - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 11:24:50,231 - main - INFO - 🔄 使用内置API实现
2025-07-28 11:24:50,231 - main - INFO - 🔧 注册内置API端点
2025-07-28 11:24:50,231 - main - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 11:24:50,231 - main - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 12:45:10,812 - main - INFO - 👋 算法管理平台专业版关闭
2025-07-28 12:52:44,133 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 12:52:44,133 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 12:52:44,133 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 12:52:44,135 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 12:52:44,135 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 12:52:44,135 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 12:52:44,135 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 12:52:44,681 - main - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 12:52:44,682 - main - INFO - 🔄 使用内置API实现
2025-07-28 12:52:44,682 - main - INFO - 🔧 注册内置API端点
2025-07-28 12:52:44,682 - main - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 12:52:44,682 - main - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 12:55:08,424 - main - INFO - 👋 算法管理平台专业版关闭
2025-07-28 12:56:46,402 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 12:56:46,402 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 12:56:46,402 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 12:56:46,404 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 12:56:46,404 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 12:56:46,404 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 12:56:46,404 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 12:56:46,435 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 12:56:46,435 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 12:56:46,436 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 12:56:46,437 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 12:56:46,437 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 12:59:47,173 - main_professional - INFO - 👋 算法管理平台专业版关闭
2025-07-28 12:59:54,241 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 12:59:54,241 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 12:59:54,241 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 12:59:54,242 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 12:59:54,242 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 12:59:54,243 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 12:59:54,243 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 12:59:54,261 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 12:59:54,261 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 12:59:54,261 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 12:59:54,261 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 12:59:54,261 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:04:05,891 - main_professional - INFO - 👋 算法管理平台专业版关闭
2025-07-28 13:04:10,756 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:04:10,756 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:04:10,756 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:04:10,757 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:04:10,757 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:04:10,757 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:04:10,757 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:04:10,777 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:04:10,777 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 13:04:10,777 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 13:04:10,778 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:04:10,778 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:04:56,522 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:04:56,523 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:04:56,523 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:04:56,524 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:04:56,524 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:04:56,524 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:04:56,524 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:04:56,542 - main - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:04:56,542 - main - INFO - 🔄 使用内置API实现
2025-07-28 13:04:56,542 - main - INFO - 🔧 注册内置API端点
2025-07-28 13:04:56,543 - main - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:04:56,543 - main - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:04:56,544 - main - INFO - 👋 算法管理平台专业版关闭
2025-07-28 13:06:00,457 - main_professional - INFO - 👋 算法管理平台专业版关闭
2025-07-28 13:06:13,132 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:06:13,132 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:06:13,132 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:06:13,134 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:06:13,134 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:06:13,134 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:06:13,134 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:06:13,156 - main - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:06:13,156 - main - INFO - 🔄 使用内置API实现
2025-07-28 13:06:13,156 - main - INFO - 🔧 注册内置API端点
2025-07-28 13:06:13,157 - main - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:06:13,158 - main - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:07:26,337 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:07:26,337 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:07:26,338 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:07:26,339 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:07:26,339 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:07:26,339 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:07:26,339 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:07:26,353 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:07:26,353 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 13:07:26,353 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 13:07:26,354 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:07:26,354 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:11:23,786 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:11:23,786 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:11:23,786 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:11:23,788 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:11:23,788 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:11:23,788 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:11:23,788 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:11:23,816 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:11:23,816 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 13:11:23,816 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 13:11:23,817 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:11:23,817 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:20:52,339 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:20:52,339 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:20:52,339 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:20:52,341 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:20:52,341 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:20:52,341 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:20:52,341 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:20:52,362 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:20:52,362 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 13:20:52,362 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 13:20:52,363 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:20:52,363 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:21:31,290 - main_professional - ERROR - 全局异常: name 'subprocess' is not defined
Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/src/main_professional.py", line 333, in stop_algorithm
    result = subprocess.run([
             ^^^^^^^^^^
NameError: name 'subprocess' is not defined

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/middleware/errors.py", line 164, in __call__
    await self.app(scope, receive, _send)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 93, in __call__
    await self.simple_response(scope, receive, send, request_headers=headers)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/middleware/cors.py", line 144, in simple_response
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/middleware/exceptions.py", line 63, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 716, in __call__
    await self.middleware_stack(scope, receive, send)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 736, in app
    await route.handle(scope, receive, send)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 290, in handle
    await self.app(scope, receive, send)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 78, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 53, in wrapped_app
    raise exc
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/starlette/routing.py", line 75, in app
    response = await f(request)
               ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 302, in app
    raw_response = await run_endpoint_function(
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/.venv/lib/python3.11/site-packages/fastapi/routing.py", line 213, in run_endpoint_function
    return await dependant.call(**values)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Desktop/工作/Project/电信/温州永嘉接口/yongjia_traffic_accident/algorithm-platform-manager/backend/src/main_professional.py", line 345, in stop_algorithm
    except subprocess.CalledProcessError as e:
           ^^^^^^^^^^
NameError: name 'subprocess' is not defined
2025-07-28 13:22:14,131 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:22:14,132 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:22:14,132 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:22:14,133 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:22:14,133 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:22:14,133 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:22:14,134 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:22:14,152 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:22:14,152 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 13:22:14,152 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 13:22:14,153 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:22:14,153 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:22:59,885 - main_professional - INFO - 算法容器 wenzhou_face_test 停止成功
2025-07-28 13:23:16,628 - main_professional - INFO - 算法容器 wenzhou_face_test 启动成功
2025-07-28 13:27:06,872 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:27:06,873 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:27:06,873 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:27:06,875 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:27:06,875 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:27:06,875 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:27:06,876 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:27:06,902 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:27:06,902 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 13:27:06,902 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 13:27:06,903 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:27:06,903 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:28:16,768 - main_professional - INFO - 算法容器 wenzhou_face_test 停止成功
2025-07-28 13:28:35,270 - main_professional - INFO - 算法容器 wenzhou_face_test 启动成功
2025-07-28 13:30:39,416 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:30:39,416 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:30:39,417 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:30:39,418 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:30:39,418 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:30:39,418 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:30:39,418 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:30:39,435 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:30:39,435 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 13:30:39,436 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 13:30:39,437 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:30:39,437 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:31:53,649 - main_professional - INFO - 算法容器 wenzhou_face_test 停止成功
2025-07-28 13:32:30,951 - main_professional - INFO - 算法容器 wenzhou_face_test 启动成功
2025-07-28 13:34:16,903 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:34:16,903 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:34:16,903 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:34:16,905 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:34:16,905 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:34:16,905 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:34:16,905 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:34:16,925 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:34:16,925 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 13:34:16,925 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 13:34:16,926 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:34:16,926 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:34:22,006 - main_professional - WARNING - 获取算法容器统计失败: name 'json' is not defined
2025-07-28 13:34:23,054 - main_professional - WARNING - 获取算法容器统计失败: name 'json' is not defined
2025-07-28 13:34:24,102 - main_professional - WARNING - 获取算法容器统计失败: name 'json' is not defined
2025-07-28 13:34:25,183 - main_professional - WARNING - 获取算法容器统计失败: name 'json' is not defined
2025-07-28 13:34:45,894 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:34:45,894 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:34:45,894 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:34:45,896 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:34:45,896 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:34:45,896 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:34:45,896 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:34:45,916 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:34:45,916 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 13:34:45,916 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 13:34:45,917 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:34:45,917 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:36:17,002 - main_professional - INFO - 算法容器 wenzhou_face_test 停止成功
2025-07-28 13:37:11,946 - main_professional - INFO - 算法容器 wenzhou_face_test 启动成功
2025-07-28 13:43:52,624 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:43:52,624 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:43:52,624 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:43:52,626 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:43:52,626 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:43:52,626 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:43:52,626 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:43:52,650 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:43:52,650 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 13:43:52,650 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 13:43:52,651 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:43:52,651 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 13:46:47,809 - main_professional - ERROR - 测试算法功能失败: name 'json' is not defined
2025-07-28 13:47:23,502 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:47:23,502 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 13:47:23,503 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 13:47:23,505 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 13:47:23,505 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 13:47:23,505 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 13:47:23,505 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 13:47:23,528 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 13:47:23,528 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 13:47:23,529 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 13:47:23,530 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 13:47:23,530 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 14:43:15,328 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 14:43:15,328 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 14:43:15,328 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 14:43:15,331 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 14:43:15,331 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 14:43:15,331 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 14:43:15,331 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 14:43:15,362 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 14:43:15,362 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 14:43:15,363 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 14:43:15,364 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 14:43:15,364 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
2025-07-28 15:53:48,023 - __main__ - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 15:53:48,023 - __main__ - INFO - 🔄 使用内置API实现
2025-07-28 15:53:48,024 - __main__ - INFO - 🔧 注册内置API端点
2025-07-28 15:53:48,026 - __main__ - INFO - 🚀 启动算法管理平台专业版: 0.0.0.0:8100
2025-07-28 15:53:48,026 - __main__ - INFO - 📚 API文档: http://0.0.0.0:8100/docs
2025-07-28 15:53:48,026 - __main__ - INFO - 🔧 环境: uv虚拟环境专业版
2025-07-28 15:53:48,026 - __main__ - INFO - 👥 工作进程: 1
2025-07-28 15:53:48,052 - main_professional - WARNING - ⚠️  完整模块导入失败: attempted relative import beyond top-level package
2025-07-28 15:53:48,052 - main_professional - INFO - 🔄 使用内置API实现
2025-07-28 15:53:48,052 - main_professional - INFO - 🔧 注册内置API端点
2025-07-28 15:53:48,054 - main_professional - INFO - 🚀 算法管理平台专业版启动中...
2025-07-28 15:53:48,054 - main_professional - INFO - 🎉 算法管理平台专业版启动完成
