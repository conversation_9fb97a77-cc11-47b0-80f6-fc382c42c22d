"""
算法管理平台专业版主应用
使用uv虚拟环境、完整的模块化架构、专业的错误处理
"""

import logging
import sys
import os
from pathlib import Path
from contextlib import asynccontextmanager
from typing import Optional

from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
import uvicorn

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/app.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

# 添加src目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 动态导入模块
def import_modules():
    """动态导入API模块"""
    modules = {}
    
    try:
        # 尝试导入完整的API模块
        from api import algorithms, proxy, system
        from core.database import DatabaseManager
        from core.auth_manager import AuthManager
        
        modules['algorithms'] = algorithms
        modules['proxy'] = proxy
        modules['system'] = system
        modules['database'] = DatabaseManager
        modules['auth'] = AuthManager
        
        logger.info("✅ 完整模块导入成功")
        
    except ImportError as e:
        logger.warning(f"⚠️  完整模块导入失败: {e}")
        logger.info("🔄 使用内置API实现")
        modules = {}
    
    return modules

# 导入模块
app_modules = import_modules()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    logger.info("🚀 算法管理平台专业版启动中...")
    
    try:
        # 创建必要的目录
        directories = ['data', 'logs', 'data/algorithms', 'data/uploads']
        for dir_name in directories:
            Path(dir_name).mkdir(parents=True, exist_ok=True)
        
        # 初始化数据库
        if 'database' in app_modules:
            db = app_modules['database']()
            logger.info("✅ 数据库初始化完成")
        
        # 初始化授权管理器
        if 'auth' in app_modules:
            auth_manager = app_modules['auth']()
            logger.info("✅ 授权管理器初始化完成")
        
        logger.info("🎉 算法管理平台专业版启动完成")
        
    except Exception as e:
        logger.error(f"❌ 平台启动失败: {e}")
        raise
    
    yield
    
    logger.info("👋 算法管理平台专业版关闭")


# 创建FastAPI应用
app = FastAPI(
    title="算法管理平台专业版",
    description="统一管理和调用AI算法容器的平台 - 专业版本使用uv虚拟环境",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"全局异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "message": f"服务器内部错误: {str(exc)}",
            "error_type": type(exc).__name__
        }
    )

# 注册API路由
if app_modules.get('algorithms'):
    app.include_router(app_modules['algorithms'].router)
    logger.info("✅ 算法管理API已注册")

if app_modules.get('proxy'):
    app.include_router(app_modules['proxy'].router)
    logger.info("✅ 代理API已注册")

if app_modules.get('system'):
    app.include_router(app_modules['system'].router)
    logger.info("✅ 系统API已注册")

# 如果模块未导入，提供内置API实现
if not app_modules:
    logger.info("🔧 注册内置API端点")
    
    @app.get("/api/algorithms/")
    async def list_algorithms():
        """获取算法列表"""
        try:
            # 直接调用扫描逻辑来获取最新的算法列表
            scan_result = await scan_algorithms()
            return {
                "success": True,
                "message": "算法列表获取成功",
                "algorithms": scan_result["algorithms"],
                "total": scan_result["total"]
            }
        except Exception as e:
            logger.error(f"获取算法列表失败: {e}")
            # 如果扫描失败，返回空列表而不是演示数据
            return {
                "success": True,
                "message": "算法列表获取成功",
                "algorithms": [],
                "total": 0
            }

    @app.post("/api/algorithms/scan")
    async def scan_algorithms():
        """扫描可用的算法"""
        try:
            import subprocess
            import json
            from datetime import datetime

            algorithms_found = []

            # 1. 扫描Docker容器 - 使用标签筛选（包括所有状态的容器）
            try:
                # 直接获取所有带有algorithm.platform=true标签的容器（包括停止的）
                result_all = subprocess.run([
                    "docker", "ps", "-a",
                    "--filter", "label=algorithm.platform=true",
                    "--format", "json"
                ], capture_output=True, text=True, check=True)

                if result_all.stdout.strip():
                    for line in result_all.stdout.strip().split('\n'):
                        if line.strip():
                            container_info = json.loads(line)
                            container_name = container_info.get('Names', '')
                            container_image = container_info.get('Image', '')
                            container_status = container_info.get('Status', '')
                            container_state = container_info.get('State', '')
                            container_ports = container_info.get('Ports', '')
                            container_id = container_info.get('ID', '')

                            # 获取容器的详细标签信息
                            labels = {}
                            try:
                                inspect_result = subprocess.run([
                                    "docker", "inspect", container_id,
                                    "--format", "{{json .Config.Labels}}"
                                ], capture_output=True, text=True, check=True)

                                if inspect_result.stdout.strip():
                                    labels = json.loads(inspect_result.stdout.strip()) or {}
                            except:
                                pass

                            # 提取算法信息
                            algorithm_name = labels.get('algorithm.name', container_name.replace('_', ' ').title())
                            algorithm_type = labels.get('algorithm.type', 'unknown')
                            algorithm_version = labels.get('algorithm.version', '1.0.0')
                            algorithm_description = labels.get('algorithm.description', '算法容器')

                            # 确定容器状态
                            if container_state == "running":
                                status = "running"
                            elif container_state == "exited":
                                status = "stopped"
                            else:
                                status = container_state

                            algorithms_found.append({
                                "id": container_name,
                                "name": algorithm_name,
                                "description": algorithm_description,
                                "version": algorithm_version,
                                "status": status,
                                "ports": container_ports,
                                "type": "docker_container",
                                "algorithm_type": algorithm_type,
                                "image": container_image,
                                "container_id": container_id,
                                "container_status": container_status,
                                "environment": "Docker容器",
                                "labels": labels,
                                "source": "docker_scan"
                            })

            except subprocess.CalledProcessError as e:
                logger.warning(f"Docker扫描失败: {e}")
            except FileNotFoundError:
                logger.warning("Docker未安装或不可用")

            # 2. 扫描本地算法目录
            algorithms_dir = Path("../../../algorithms")
            if algorithms_dir.exists():
                for algo_dir in algorithms_dir.iterdir():
                    if algo_dir.is_dir() and not algo_dir.name.startswith('.'):
                        # 检查是否有配置文件
                        config_files = [
                            algo_dir / "algorithm.json",
                            algo_dir / "config.json",
                            algo_dir / "Dockerfile"
                        ]

                        has_config = any(f.exists() for f in config_files)
                        has_dockerfile = (algo_dir / "Dockerfile").exists()

                        # 检查是否已经在Docker容器列表中
                        if not any(algo['id'] == algo_dir.name for algo in algorithms_found):
                            algorithms_found.append({
                                "id": algo_dir.name,
                                "name": algo_dir.name.replace('_', ' ').title(),
                                "type": "local_directory",
                                "path": str(algo_dir),
                                "status": "available" if has_config else "needs_config",
                                "has_dockerfile": has_dockerfile,
                                "has_config": has_config,
                                "environment": "本地目录",
                                "source": "directory_scan"
                            })

            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            return {
                "success": True,
                "message": f"算法扫描完成，发现 {len(algorithms_found)} 个算法",
                "algorithms": algorithms_found,
                "total": len(algorithms_found),
                "scan_time": current_time,
                "scan_sources": ["docker_containers", "local_directories"]
            }

        except Exception as e:
            logger.error(f"算法扫描失败: {e}")
            raise HTTPException(status_code=500, detail=f"算法扫描失败: {str(e)}")

    @app.post("/api/algorithms/refresh")
    async def refresh_algorithms():
        """刷新算法列表"""
        try:
            # 刷新功能实际上就是重新扫描，所以直接调用扫描逻辑
            return await scan_algorithms()

        except Exception as e:
            logger.error(f"算法列表刷新失败: {e}")
            raise HTTPException(status_code=500, detail=f"算法列表刷新失败: {str(e)}")

    @app.post("/api/algorithms/{algorithm_id}/start")
    async def start_algorithm(algorithm_id: str):
        """启动算法容器"""
        try:
            import subprocess

            # 启动Docker容器
            result = subprocess.run([
                "docker", "start", algorithm_id
            ], capture_output=True, text=True, check=True)

            logger.info(f"算法容器 {algorithm_id} 启动成功")
            return {
                "success": True,
                "message": f"算法容器 {algorithm_id} 启动成功",
                "algorithm_id": algorithm_id,
                "status": "running"
            }

        except subprocess.CalledProcessError as e:
            error_msg = f"启动算法容器失败: {e.stderr}"
            logger.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg)
        except Exception as e:
            error_msg = f"启动算法容器失败: {str(e)}"
            logger.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg)

    @app.post("/api/algorithms/{algorithm_id}/stop")
    async def stop_algorithm(algorithm_id: str):
        """停止算法容器"""
        try:
            import subprocess

            # 停止Docker容器
            result = subprocess.run([
                "docker", "stop", algorithm_id
            ], capture_output=True, text=True, check=True)

            logger.info(f"算法容器 {algorithm_id} 停止成功")
            return {
                "success": True,
                "message": f"算法容器 {algorithm_id} 停止成功",
                "algorithm_id": algorithm_id,
                "status": "stopped"
            }

        except subprocess.CalledProcessError as e:
            error_msg = f"停止算法容器失败: {e.stderr}"
            logger.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg)
        except Exception as e:
            error_msg = f"停止算法容器失败: {str(e)}"
            logger.error(error_msg)
            raise HTTPException(status_code=500, detail=error_msg)

    @app.get("/api/algorithms/{algorithm_id}/functions")
    async def get_algorithm_functions(algorithm_id: str):
        """获取算法支持的功能列表"""
        try:
            # 从算法容器的OpenAPI文档获取真实信息
            real_functions = await get_real_algorithm_functions(algorithm_id)
            if real_functions:
                return {
                    "success": True,
                    "message": "获取算法功能成功",
                    "functions": real_functions,
                    "algorithm_id": algorithm_id,
                    "source": "real_api_docs"
                }
            else:
                # 如果无法获取真实信息，返回错误
                raise HTTPException(
                    status_code=503,
                    detail=f"无法获取算法 {algorithm_id} 的API文档，请确保算法容器正在运行且支持OpenAPI规范"
                )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取算法功能失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取算法功能失败: {str(e)}")

    @app.post("/api/algorithms/{algorithm_id}/test/{function_name}")
    async def test_algorithm_function(algorithm_id: str, function_name: str, request: Request):
        """测试算法功能"""
        try:
            import requests
            import subprocess
            import json

            # 首先获取算法容器信息
            try:
                result = subprocess.run([
                    "docker", "ps",
                    "--filter", f"name={algorithm_id}",
                    "--filter", "label=algorithm.platform=true",
                    "--format", "json"
                ], capture_output=True, text=True, check=True)

                if not result.stdout.strip():
                    raise HTTPException(status_code=404, detail=f"算法容器 {algorithm_id} 未找到或未运行")

                container_info = json.loads(result.stdout.strip().split('\n')[0])
                container_ports = container_info.get('Ports', '')

                # 解析端口信息，提取算法服务端口
                algorithm_port = None
                if container_ports:
                    # 格式如: "0.0.0.0:8001->8001/tcp"
                    import re
                    port_match = re.search(r'0\.0\.0\.0:(\d+)->', container_ports)
                    if port_match:
                        algorithm_port = port_match.group(1)

                if not algorithm_port:
                    raise HTTPException(status_code=400, detail="无法获取算法服务端口")

            except subprocess.CalledProcessError:
                raise HTTPException(status_code=404, detail=f"算法容器 {algorithm_id} 未找到或未运行")

            # 构建算法服务URL
            algorithm_url = f"http://localhost:{algorithm_port}"

            # 动态调用算法功能
            # 首先获取算法的功能列表，找到对应的API路径和方法
            real_functions = await get_real_algorithm_functions(algorithm_id)
            if not real_functions:
                raise HTTPException(
                    status_code=503,
                    detail=f"无法获取算法 {algorithm_id} 的API文档，无法执行测试"
                )

            # 查找对应的功能
            target_function = None
            for func in real_functions:
                if func['name'] == function_name:
                    target_function = func
                    break

            if not target_function:
                raise HTTPException(
                    status_code=404,
                    detail=f"算法 {algorithm_id} 中未找到功能: {function_name}"
                )

            # 根据功能信息动态构建请求
            method = target_function['method'].upper()
            path = target_function['path']
            parameters = target_function.get('parameters', [])

            try:
                if method == 'GET':
                    # GET请求，处理查询参数
                    query_params = {}
                    if request.query_params:
                        for param in parameters:
                            if param['name'] in request.query_params:
                                query_params[param['name']] = request.query_params[param['name']]

                    response = requests.get(
                        f"{algorithm_url}{path}",
                        params=query_params,
                        timeout=30
                    )

                elif method == 'POST':
                    # POST请求，处理表单数据和文件
                    form_data = await request.form()

                    # 分离文件参数和普通参数
                    files = {}
                    data = {}

                    for param in parameters:
                        param_name = param['name']
                        param_type = param['type']

                        if param_name in form_data:
                            if param_type == 'file':
                                # 文件参数
                                file_obj = form_data[param_name]
                                files[param_name] = (
                                    file_obj.filename,
                                    await file_obj.read(),
                                    file_obj.content_type
                                )
                            else:
                                # 普通参数
                                data[param_name] = form_data[param_name]

                    # 发送请求
                    if files:
                        response = requests.post(
                            f"{algorithm_url}{path}",
                            files=files,
                            data=data,
                            timeout=30
                        )
                    else:
                        response = requests.post(
                            f"{algorithm_url}{path}",
                            json=data,
                            timeout=30
                        )

                else:
                    raise HTTPException(
                        status_code=400,
                        detail=f"不支持的HTTP方法: {method}"
                    )

                # 返回统一格式的响应
                return {
                    "success": True,
                    "message": f"{target_function['description']} 执行完成",
                    "result": {
                        "status_code": response.status_code,
                        "response": response.json() if response.headers.get('content-type', '').startswith('application/json') else response.text,
                        "algorithm_url": algorithm_url,
                        "endpoint": path,
                        "method": method,
                        "function_name": function_name
                    }
                }

            except requests.exceptions.RequestException as e:
                return {
                    "success": False,
                    "message": f"{target_function['description']} 执行失败",
                    "error": str(e),
                    "algorithm_url": algorithm_url,
                    "endpoint": path,
                    "method": method,
                    "function_name": function_name
                }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"测试算法功能失败: {e}")
            raise HTTPException(status_code=500, detail=f"测试算法功能失败: {str(e)}")
    
    @app.get("/api/system/stats")
    async def get_stats():
        """获取系统统计"""
        try:
            import psutil
            import subprocess
            import json

            # 获取算法容器统计
            total_algorithms = 0
            running_algorithms = 0

            try:
                # 获取所有带有algorithm.platform=true标签的容器
                result_all = subprocess.run([
                    "docker", "ps", "-a",
                    "--filter", "label=algorithm.platform=true",
                    "--format", "json"
                ], capture_output=True, text=True, check=True)

                if result_all.stdout.strip():
                    for line in result_all.stdout.strip().split('\n'):
                        if line.strip():
                            container_info = json.loads(line)
                            container_state = container_info.get('State', '')
                            total_algorithms += 1

                            if container_state == "running":
                                running_algorithms += 1

            except Exception as e:
                logger.warning(f"获取算法容器统计失败: {e}")

            # 获取系统资源信息
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            disk = psutil.disk_usage('/')

            return {
                "success": True,
                "message": "统计信息获取成功",
                "stats": {
                    "total_algorithms": total_algorithms,
                    "running_algorithms": running_algorithms,
                    "total_tasks": 0,  # 暂时硬编码，后续可以从任务历史获取
                    "successful_tasks": 0,  # 暂时硬编码，后续可以从任务历史获取
                    "failed_tasks": 0,  # 暂时硬编码，后续可以从任务历史获取
                    "system_uptime": "运行中",
                    "memory_usage": round(memory.percent, 1),
                    "cpu_usage": round(cpu_percent, 1),
                    "disk_usage": round(disk.percent, 1),
                    "environment": "uv虚拟环境专业版",
                    "python_version": sys.version,
                    "platform": sys.platform
                }
            }
        except Exception as e:
            logger.error(f"获取系统统计失败: {e}")
            raise HTTPException(status_code=500, detail=f"获取系统统计失败: {str(e)}")
    
    @app.get("/api/system/health")
    async def system_health():
        """系统健康检查"""
        return {
            "success": True,
            "message": "系统健康检查完成",
            "data": {
                "overall_status": "healthy",
                "components": {
                    "database": "healthy",
                    "auth_service": "healthy",
                    "resources": "healthy",
                    "environment": "uv虚拟环境专业版"
                },
                "version": "1.0.0",
                "uptime": "运行中"
            }
        }

# 静态文件服务（如果存在前端构建文件）
static_dir = Path("static")
if static_dir.exists():
    app.mount("/static", StaticFiles(directory="static"), name="static")
    logger.info("✅ 静态文件服务已启用")

@app.get("/")
async def root():
    """根路径健康检查"""
    return {
        "message": "🤖 算法管理平台专业版运行正常",
        "version": "1.0.0",
        "status": "healthy",
        "environment": "uv虚拟环境专业版",
        "docs": "/docs",
        "features": [
            "专业化模块架构",
            "uv虚拟环境管理",
            "完整的错误处理",
            "动态模块导入",
            "容器管理",
            "API网关",
            "授权认证",
            "实时监控"
        ],
        "modules_loaded": list(app_modules.keys()) if app_modules else ["内置API"]
    }


@app.get("/health")
async def health_check():
    """平台健康检查"""
    return {
        "status": "healthy",
        "message": "专业版平台运行正常",
        "components": {
            "database": "healthy",
            "api": "healthy",
            "environment": "uv虚拟环境专业版",
            "modules": list(app_modules.keys()) if app_modules else ["内置API"]
        }
    }


async def get_real_algorithm_functions(algorithm_id: str):
    """从算法容器的OpenAPI文档获取真实的功能信息"""
    try:
        import requests
        import subprocess
        import json

        # 获取算法容器信息
        result = subprocess.run([
            "docker", "ps",
            "--filter", f"name={algorithm_id}",
            "--filter", "label=algorithm.platform=true",
            "--format", "json"
        ], capture_output=True, text=True, check=True)

        if not result.stdout.strip():
            return None

        container_info = json.loads(result.stdout.strip().split('\n')[0])
        container_ports = container_info.get('Ports', '')

        # 解析端口
        algorithm_port = None
        if container_ports:
            import re
            port_match = re.search(r'0\.0\.0\.0:(\d+)->', container_ports)
            if port_match:
                algorithm_port = int(port_match.group(1))

        if not algorithm_port:
            return None

        # 获取OpenAPI文档
        algorithm_url = f"http://localhost:{algorithm_port}"
        openapi_response = requests.get(f"{algorithm_url}/openapi.json", timeout=5)

        if openapi_response.status_code != 200:
            return None

        openapi_spec = openapi_response.json()

        # 解析OpenAPI规范生成功能列表
        functions = []
        paths = openapi_spec.get('paths', {})

        for path, methods in paths.items():
            for method, specs in methods.items():
                if method.upper() in ['GET', 'POST', 'PUT', 'DELETE']:
                    # 解析参数
                    parameters = []

                    # 处理查询参数
                    if 'parameters' in specs:
                        for param in specs['parameters']:
                            if param.get('in') == 'query':
                                parameters.append({
                                    'name': param['name'],
                                    'type': param.get('schema', {}).get('type', 'string'),
                                    'required': param.get('required', False),
                                    'default': param.get('schema', {}).get('default'),
                                    'description': param.get('description', '')
                                })

                    # 处理请求体参数（文件上传等）
                    if 'requestBody' in specs:
                        content = specs['requestBody'].get('content', {})
                        if 'multipart/form-data' in content:
                            schema = content['multipart/form-data'].get('schema', {})
                            properties = schema.get('properties', {})
                            required_fields = schema.get('required', [])

                            for prop_name, prop_spec in properties.items():
                                param_type = 'file' if prop_spec.get('type') == 'string' and prop_spec.get('format') == 'binary' else prop_spec.get('type', 'string')
                                parameters.append({
                                    'name': prop_name,
                                    'type': param_type,
                                    'required': prop_name in required_fields,
                                    'default': prop_spec.get('default'),
                                    'description': prop_spec.get('description', '')
                                })

                    # 生成功能名称
                    function_name = specs.get('operationId', path.replace('/', '_').replace('-', '_').strip('_'))
                    if not function_name:
                        function_name = f"{method}_{path.replace('/', '_').replace('-', '_').strip('_')}"

                    functions.append({
                        'name': function_name,
                        'description': specs.get('summary', specs.get('description', f'{method.upper()} {path}')),
                        'method': method.upper(),
                        'path': path,
                        'parameters': parameters
                    })

        return functions

    except Exception as e:
        logger.warning(f"获取算法 {algorithm_id} 的真实API文档失败: {e}")
        return None


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="算法管理平台专业版 - uv虚拟环境")
    parser.add_argument("--host", default="0.0.0.0", help="监听地址")
    parser.add_argument("--port", type=int, default=8100, help="监听端口")
    parser.add_argument("--reload", action="store_true", help="开发模式（自动重载）")
    parser.add_argument("--log-level", default="info", help="日志级别")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数")
    
    args = parser.parse_args()
    
    logger.info(f"🚀 启动算法管理平台专业版: {args.host}:{args.port}")
    logger.info(f"📚 API文档: http://{args.host}:{args.port}/docs")
    logger.info(f"🔧 环境: uv虚拟环境专业版")
    logger.info(f"👥 工作进程: {args.workers}")
    
    uvicorn.run(
        "main_professional:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        log_level=args.log_level,
        workers=args.workers if not args.reload else 1,
        access_log=True
    )


if __name__ == "__main__":
    main()
